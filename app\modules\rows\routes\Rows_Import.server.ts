import { LoaderFunctionArgs, redirect, ActionFunction } from "react-router";
import { MetaTagsDto } from "~/application/dtos/seo/MetaTagsDto";
import { Colors } from "~/application/enums/shared/Colors";
import { RowsApi } from "~/utils/api/.server/RowsApi";
import UrlUtils from "~/utils/app/UrlUtils";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { getMaxRowFolio, getRowById, RowWithDetails } from "~/utils/db/entities/rows.db.server";
import RowHelper from "~/utils/helpers/RowHelper";
import RowsRequestUtils from "../utils/RowsRequestUtils";
import { adminGetAllTenantsIdsAndNames, getTenant } from "~/utils/db/tenants.db.server";
import { db } from "~/utils/db.server";

// Helper function to process relationship data
async function processRelationshipData(
  entity: EntityWithDetails,
  properties: { name: string; value: string }[],
  tenantId: string | null
): Promise<{
  regularProperties: { name: string; value: string }[];
  parentRows: { relationshipId: string; parentId: string }[];
  childRows: { relationshipId: string; childId: string }[];
}> {
  const regularProperties: { name: string; value: string }[] = [];
  const parentRows: { relationshipId: string; parentId: string }[] = [];
  const childRows: { relationshipId: string; childId: string }[] = [];

  console.log(`Processing relationship data for entity: ${entity.name}`);
  console.log(`Parent relationships: ${entity.parentEntities.length}`);
  console.log(`Child relationships: ${entity.childEntities.length}`);
  console.log(`Properties to process:`, properties.map(p => `${p.name}=${p.value}`));

  for (const property of properties) {
    const { name, value } = property;

    // Check if this is a relationship field
    let isRelationshipField = false;

    // Check parent relationships (many-to-one)
    for (const relationship of entity.parentEntities) {
      const relatedEntityName = relationship.parent.name.toLowerCase();

      if (name === `${relatedEntityName}Id`) {
        // Business identifier reference - search for the row by this identifier
        if (value && value.trim()) {
          try {
            // Search for the related row by business identifier in any text field
            const relatedRow = await db.row.findFirst({
              where: {
                entityId: relationship.parent.id,
                tenantId: tenantId,
                values: {
                  some: {
                    textValue: value.trim(),
                  },
                },
              },
            });

            if (relatedRow) {
              parentRows.push({
                relationshipId: relationship.id,
                parentId: relatedRow.id,
              });
              console.log(`Found parent relationship: ${name}=${value} -> row ID: ${relatedRow.id}`);
            } else {
              console.warn(`Could not find related ${relationship.parent.name} with identifier: ${value}`);
            }
          } catch (error) {
            console.error(`Error finding related row for ${name}:`, error);
          }
        }
        isRelationshipField = true;
        break;
      } else if (name === `${relatedEntityName}Name`) {
        // Name reference - need to find the row by name/title
        if (value && value.trim()) {
          try {
            // Find the related entity row by name/title (search in display fields first, then any text field)
            let relatedRow = await db.row.findFirst({
              where: {
                entityId: relationship.parent.id,
                tenantId: tenantId,
                values: {
                  some: {
                    property: {
                      isDisplay: true,
                    },
                    textValue: value.trim(),
                  },
                },
              },
            });

            // If not found in display fields, search in any text field
            if (!relatedRow) {
              relatedRow = await db.row.findFirst({
                where: {
                  entityId: relationship.parent.id,
                  tenantId: tenantId,
                  values: {
                    some: {
                      textValue: value.trim(),
                    },
                  },
                },
              });
            }

            if (relatedRow) {
              parentRows.push({
                relationshipId: relationship.id,
                parentId: relatedRow.id,
              });
              console.log(`Found parent relationship by name: ${name}=${value} -> row ID: ${relatedRow.id}`);
            } else {
              console.warn(`Could not find related ${relationship.parent.name} with name: ${value}`);
            }
          } catch (error) {
            console.error(`Error finding related row for ${name}:`, error);
          }
        }
        isRelationshipField = true;
        break;
      }
    }

    // Check child relationships (one-to-many) if not already processed
    if (!isRelationshipField) {
      for (const relationship of entity.childEntities) {
        const relatedEntityName = relationship.child.name.toLowerCase();

        if (name === `${relatedEntityName}Id`) {
          // Business identifier reference - search for the row by this identifier
          if (value && value.trim()) {
            try {
              // Search for the related row by business identifier in any text field
              const relatedRow = await db.row.findFirst({
                where: {
                  entityId: relationship.child.id,
                  tenantId: tenantId,
                  values: {
                    some: {
                      textValue: value.trim(),
                    },
                  },
                },
              });

              if (relatedRow) {
                childRows.push({
                  relationshipId: relationship.id,
                  childId: relatedRow.id,
                });
                console.log(`Found child relationship: ${name}=${value} -> row ID: ${relatedRow.id}`);
              } else {
                console.warn(`Could not find related ${relationship.child.name} with identifier: ${value}`);
              }
            } catch (error) {
              console.error(`Error finding related row for ${name}:`, error);
            }
          }
          isRelationshipField = true;
          break;
        } else if (name === `${relatedEntityName}Name`) {
          // Name reference - need to find the row by name/title
          if (value && value.trim()) {
            try {
              // Find the related entity row by name/title (search in display fields first, then any text field)
              let relatedRow = await db.row.findFirst({
                where: {
                  entityId: relationship.child.id,
                  tenantId: tenantId,
                  values: {
                    some: {
                      property: {
                        isDisplay: true,
                      },
                      textValue: value.trim(),
                    },
                  },
                },
              });

              // If not found in display fields, search in any text field
              if (!relatedRow) {
                relatedRow = await db.row.findFirst({
                  where: {
                    entityId: relationship.child.id,
                    tenantId: tenantId,
                    values: {
                      some: {
                        textValue: value.trim(),
                      },
                    },
                  },
                });
              }

              if (relatedRow) {
                childRows.push({
                  relationshipId: relationship.id,
                  childId: relatedRow.id,
                });
                console.log(`Found child relationship by name: ${name}=${value} -> row ID: ${relatedRow.id}`);
              } else {
                console.warn(`Could not find related ${relationship.child.name} with name: ${value}`);
              }
            } catch (error) {
              console.error(`Error finding related row for ${name}:`, error);
            }
          }
          isRelationshipField = true;
          break;
        }
      }
    }

    // If not a relationship field, add to regular properties
    if (!isRelationshipField) {
      regularProperties.push(property);
    }
  }

  return {
    regularProperties,
    parentRows,
    childRows,
  };
}

export namespace Rows_Import {
  export type LoaderData = {
    meta: MetaTagsDto;
    entity: EntityWithDetails;
    allTenants: { id: string; name: string; slug: string }[];
  };
  export const loader = async ({ request, params }: LoaderFunctionArgs) => {
    const { t, tenantId, entity } = await RowsRequestUtils.getLoader({ request, params });
    if (!entity.isAutogenerated || entity.type === "system") {
      throw redirect(tenantId ? UrlUtils.currentTenantUrl(params, "404") : "/404");
    }
    const data: LoaderData = {
      meta: [{ title: `${t("shared.import")} ${t(entity.titlePlural)} | ${process.env.APP_NAME}` }],
      entity,
      allTenants: !tenantId ? await adminGetAllTenantsIdsAndNames() : [],
    };
    return Response.json(data);
  };

  export interface ImportRow {
    properties: { name: string; value: string }[];
    row?: RowWithDetails | null;
    error?: string;
  }
  export type ActionData = {
    rows?: ImportRow[];
    error?: string;
  };
  export const action: ActionFunction = async ({ request, params }) => {
    const { t, userId, tenantId, entity, form } = await RowsRequestUtils.getAction({ request, params });
    const action = form.get("action");
    if (action === "import") {
      const tag = form.get("tag")?.toString() ?? "import";
      const rawRows: ImportRow[] = form.getAll("rows[]").map((f: FormDataEntryValue) => {
        return JSON.parse(f.toString());
      });
      let tenantToImport = tenantId;
      if (tenantId === null) {
        const selectedTenantId = form.get("selectedTenantId")?.toString() || "{null}";
        if (selectedTenantId === "{null}") {
          tenantToImport = null;
        } else {
          const existingTenant = await getTenant(selectedTenantId);
          if (!existingTenant) {
            return Response.json({ error: "Invalid tenant with ID: " + selectedTenantId }, { status: 400 });
          }
          tenantToImport = selectedTenantId;
        }
      }

      if (rawRows.length === 0) {
        return Response.json({ error: "No rows to import" }, { status: 400 });
      }
      const rows: ImportRow[] = [];
      let folio = 1;
      const maxFolio = await getMaxRowFolio({ tenantId: tenantToImport, entityId: entity.id });
      if (maxFolio && maxFolio._max.folio !== null) {
        folio = maxFolio._max.folio + 1;
      }
      await Promise.all(
        rawRows.map(async (importRow: ImportRow, idx) => {
          try {
            // Wait for keeping folios unique, I can't think of another way to do this
            await new Promise((r) => setTimeout(r, 1500));

            // Process relationship data
            const { regularProperties, parentRows, childRows } = await processRelationshipData(
              entity,
              importRow.properties,
              tenantToImport
            );

            // Debug logging
            console.log(`Processing row ${idx + 1}:`, {
              originalProperties: importRow.properties.length,
              regularProperties: regularProperties.length,
              parentRows: parentRows.length,
              childRows: childRows.length,
              parentRowIds: parentRows.map(p => p.parentId),
              childRowIds: childRows.map(c => c.childId),
            });

            // Get row values from regular properties only
            const rowValues = RowHelper.getRowPropertiesFromForm({
              t,
              entity,
              values: regularProperties
            });

            // Add relationship data to row values
            if (parentRows.length > 0 || childRows.length > 0) {
              rowValues.parentRows = parentRows;
              rowValues.childRows = childRows;
            }

            const newRow = await RowsApi.create({
              entity,
              tenantId: tenantToImport,
              userId,
              rowValues,
              nextFolio: folio + idx,
              request,
            });
            if (tag) {
              await RowsApi.addTag({ row: newRow, tag: { value: tag, color: Colors.INDIGO } });
            }
            importRow.row = await getRowById(newRow.id);
          } catch (e: any) {
            importRow.error = e.message?.toString();
          }
          rows.push(importRow);
        })
      );
      const data: ActionData = {
        rows,
      };
      return data;
    } else {
      return Response.json({ error: "Invalid form" }, { status: 400 });
    }
  };
}
